<?php return array (
  'providers' => 
  array (
    0 => 'Illuminate\\Bus\\BusServiceProvider',
    1 => 'Illuminate\\Cache\\CacheServiceProvider',
    2 => 'Illuminate\\Cookie\\CookieServiceProvider',
    3 => 'Illuminate\\Database\\DatabaseServiceProvider',
    4 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
    5 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
    6 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
    7 => 'Illuminate\\Hashing\\HashServiceProvider',
    8 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    9 => 'Illuminate\\Queue\\QueueServiceProvider',
    10 => 'Illuminate\\Session\\SessionServiceProvider',
    11 => 'Illuminate\\Translation\\TranslationServiceProvider',
    12 => 'Illuminate\\Validation\\ValidationServiceProvider',
    13 => 'Roots\\Acorn\\Assets\\AssetsServiceProvider',
    14 => 'Roots\\Acorn\\Filesystem\\FilesystemServiceProvider',
    15 => 'Roots\\Acorn\\Providers\\AcornServiceProvider',
    16 => 'Roots\\Acorn\\Providers\\QueueServiceProvider',
    17 => 'Roots\\Acorn\\View\\ViewServiceProvider',
    18 => 'Illuminate\\Foundation\\Providers\\ComposerServiceProvider',
    19 => 'Illuminate\\Database\\MigrationServiceProvider',
    20 => 'Carbon\\Laravel\\ServiceProvider',
    21 => 'Termwind\\Laravel\\TermwindServiceProvider',
    22 => 'App\\Providers\\ThemeServiceProvider',
  ),
  'eager' => 
  array (
    0 => 'Illuminate\\Cookie\\CookieServiceProvider',
    1 => 'Illuminate\\Database\\DatabaseServiceProvider',
    2 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
    3 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
    4 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
    5 => 'Illuminate\\Session\\SessionServiceProvider',
    6 => 'Roots\\Acorn\\Assets\\AssetsServiceProvider',
    7 => 'Roots\\Acorn\\Filesystem\\FilesystemServiceProvider',
    8 => 'Roots\\Acorn\\Providers\\AcornServiceProvider',
    9 => 'Roots\\Acorn\\Providers\\QueueServiceProvider',
    10 => 'Roots\\Acorn\\View\\ViewServiceProvider',
    11 => 'Carbon\\Laravel\\ServiceProvider',
    12 => 'Termwind\\Laravel\\TermwindServiceProvider',
    13 => 'App\\Providers\\ThemeServiceProvider',
  ),
  'deferred' => 
  array (
    'Illuminate\\Bus\\Dispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Contracts\\Bus\\Dispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Contracts\\Bus\\QueueingDispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Bus\\BatchRepository' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Bus\\DatabaseBatchRepository' => 'Illuminate\\Bus\\BusServiceProvider',
    'cache' => 'Illuminate\\Cache\\CacheServiceProvider',
    'cache.store' => 'Illuminate\\Cache\\CacheServiceProvider',
    'cache.psr6' => 'Illuminate\\Cache\\CacheServiceProvider',
    'memcached.connector' => 'Illuminate\\Cache\\CacheServiceProvider',
    'Illuminate\\Cache\\RateLimiter' => 'Illuminate\\Cache\\CacheServiceProvider',
    'hash' => 'Illuminate\\Hashing\\HashServiceProvider',
    'hash.driver' => 'Illuminate\\Hashing\\HashServiceProvider',
    'Illuminate\\Contracts\\Pipeline\\Hub' => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    'pipeline' => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    'queue' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.connection' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.failer' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.listener' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.worker' => 'Illuminate\\Queue\\QueueServiceProvider',
    'translator' => 'Illuminate\\Translation\\TranslationServiceProvider',
    'translation.loader' => 'Illuminate\\Translation\\TranslationServiceProvider',
    'validator' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'validation.presence' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'Illuminate\\Contracts\\Validation\\UncompromisedVerifier' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'composer' => 'Illuminate\\Foundation\\Providers\\ComposerServiceProvider',
    'migrator' => 'Illuminate\\Database\\MigrationServiceProvider',
    'migration.repository' => 'Illuminate\\Database\\MigrationServiceProvider',
    'migration.creator' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Migrations\\Migrator' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\MigrateCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\FreshCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\InstallCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\RefreshCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\ResetCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\RollbackCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\StatusCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\MigrateMakeCommand' => 'Illuminate\\Database\\MigrationServiceProvider',
  ),
  'when' => 
  array (
    'Illuminate\\Bus\\BusServiceProvider' => 
    array (
    ),
    'Illuminate\\Cache\\CacheServiceProvider' => 
    array (
    ),
    'Illuminate\\Hashing\\HashServiceProvider' => 
    array (
    ),
    'Illuminate\\Pipeline\\PipelineServiceProvider' => 
    array (
    ),
    'Illuminate\\Queue\\QueueServiceProvider' => 
    array (
    ),
    'Illuminate\\Translation\\TranslationServiceProvider' => 
    array (
    ),
    'Illuminate\\Validation\\ValidationServiceProvider' => 
    array (
    ),
    'Illuminate\\Foundation\\Providers\\ComposerServiceProvider' => 
    array (
    ),
    'Illuminate\\Database\\MigrationServiceProvider' => 
    array (
    ),
  ),
);