{"translation-revision-date": "2025-07-29 13:22:09+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_ZA"}, "Word count type. Do not translate!\u0004words": ["words"], "Cannot inherit the current template query when placed inside the singular content (e.g., post, page, 404, blank).": ["Cannot inherit the current template query when placed inside the singular content (e.g., post, page, 404, blank)."], "Write summary. Press Enter to expand or collapse the details.": ["Write summary. Press Enter to expand or collapse the details."], "Muted because of Autoplay.": ["Muted because of Autoplay."], "Subheading": ["Subheading"], "Include headings from all pages (if the post is paginated).": ["Include headings from all pages (if the post is paginated)."], "Provide a text label or use the default.": ["Provide a text label or use the default."], "Show search label": ["Show search label"], "text tracks\u0004Edit %s": ["Edit %s"], "archive label\u0004%1$s: %2$s": ["%1$s: %2$s"], "Query type": ["Query type"], "date order\u0004dmy": ["dmy"], "List style": ["List style"], "La Mancha": ["La Mancha"], "%s Embed": ["%s oEmbed"], "Terms List": ["Terms List"], "When enabled, videos will play directly within the webpage on mobile browsers, instead of opening in a fullscreen player.": ["When enabled, videos will play directly within the webpage on mobile browsers, instead of opening in a fullscreen player."], "Template Part \"%s\" updated.": ["Template Part \"%s\" updated."], "Show text": ["Show text"], "Enter social link": ["Enter social link"], "RSS block display setting\u0004Grid view": ["Grid view"], "RSS block display setting\u0004List view": ["List view"], "RSS URL": ["RSS URL"], "Remove citation": ["Remove citation"], "Experimental full-page client-side navigation setting enabled.": ["Experimental full-page client-side navigation setting enabled."], "Post template block display setting\u0004Grid view": ["Grid view"], "Post template block display setting\u0004List view": ["List view"], "Latest posts block display setting\u0004Grid view": ["Grid view"], "Latest posts block display setting\u0004List view": ["List view"], "Embed a Bluesky post.": ["Embed a <PERSON><PERSON> post."], "Divide into columns. Select a layout:": ["Divide into columns. Select a layout:"], "https://www.w3.org/WAI/tutorials/images/decision-tree/": ["https://www.w3.org/WAI/tutorials/images/decision-tree/"], "Currently, avoiding full page reloads is not possible when non-interactive or non-client Navigation compatible blocks from plugins are present inside the Query block.": ["Currently, avoiding full page reloads is not possible when non-interactive or non-clientNavigation compatible blocks from plugins are present inside the Query block."], "Items per page": ["Items per page"], "Edit original": ["Edit original"], "Only link to posts that have the same taxonomy terms as the current post. For example the same tags or categories.": ["Only link to posts that have the same taxonomy terms as the current post. For example the same tags or categories."], "Filter by taxonomy": ["Filter by taxonomy"], "Unfiltered": ["Unfiltered"], "This Navigation Menu displays your website's pages. Editing it will enable you to add, delete, or reorder pages. However, new pages will no longer be added automatically.": ["This Navigation Menu displays your website's pages. Editing it will enable you to add, delete, or reorder pages. However, new pages will no longer be added automatically."], "Pattern \"%s\" cannot be rendered inside itself.": ["Pattern \"%s\" cannot be rendered inside itself."], "Search for and add a link to your Navigation.": ["Search for and add a link to your Navigation."], "Choose a block to add to your Navigation.": ["Choose a block to add to your Navigation."], "Crop image to fill": ["Crop image to fill"], "Upload to Media Library": ["Upload to Media Library"], "Connected to dynamic data": ["Connected to dynamic data"], "Connected to %s": ["Connected to %s"], "HTML preview is not yet fully accessible. Please switch screen reader to virtualized mode to navigate the below iFrame.": ["HTML preview is not yet fully accessible. Please switch screen reader to virtualised mode to navigate the below iFrame."], "Custom HTML Preview": ["Custom HTML Preview"], "Open images in new tab": ["Open images in new tab"], "Randomize order": ["Randomise order"], "Crop images to fit": ["Crop images to fit"], "Add gallery caption": ["Add gallery caption"], "Error/failure message for form submissions.": ["Error/failure message for form submissions."], "Form Submission Error": ["Form Submission Error"], "Your form has been submitted successfully.": ["Your form has been submitted successfully."], "Success message for form submissions.": ["Success message for form submissions."], "Form Submission Success": ["Form Submission Success"], "Submission error notification": ["Submission error notification"], "Submission success notification": ["Submission success notification"], "Enter the message you wish displayed for form submission error/success, and select the type of the message (success/error) from the block's options.": ["Enter the message you wish displayed for form submission error/success, and select the type of the message (success/error) from the block's options."], "A numeric input.": ["A numeric input."], "Number Input": ["Number Input"], "Used for phone numbers.": ["Used for phone numbers."], "Telephone Input": ["Telephone Input"], "Used for URLs.": ["Used for URLs."], "URL Input": ["URL Input"], "Used for email addresses.": ["Used for email addresses."], "Email Input": ["Email Input"], "A simple checkbox input.": ["A simple checkbox input."], "Checkbox Input": ["Checkbox Input"], "A textarea input to allow entering multiple lines of text.": ["A textarea input to allow entering multiple lines of text."], "Textarea Input": ["Textarea Input"], "A generic text input.": ["A generic text input."], "Text Input": ["Text Input"], "Type the label for this input": ["Type the label for this input"], "Empty label": ["Empty label"], "Value": ["Value"], "Required": ["Required"], "Inline label": ["Inline label"], "Request data deletion": ["Request data deletion"], "Request data export": ["Request data export"], "To request an export or deletion of your personal data on this site, please fill-in the form below. You can define the type of request you wish to perform, and your email address. Once the form is submitted, you will receive a confirmation email with instructions on the next steps.": ["To request an export or deletion of your personal data on this site, please fill-in the form below. You can define the type of request you wish to perform, and your email address. Once the form is submitted, you will receive a confirmation email with instructions on the next steps."], "A form to request data exports and/or deletion.": ["A form to request data exports and/or deletion."], "A comment form for posts and pages.": ["A comment form for posts and pages."], "Experimental Comment form": ["Experimental Comment form"], "The URL where the form should be submitted.": ["The URL where the form should be submitted."], "Form action": ["Form action"], "Method": ["Method"], "The email address where form submissions will be sent. Separate multiple email addresses with a comma.": ["The email address where form submissions will be sent. Separate multiple email addresses with a comma."], "Email for form submissions": ["Email for form submissions"], "Select the method to use for form submissions.": ["Select the method to use for form submissions."], "Select the method to use for form submissions. Additional options for the \"custom\" mode can be found in the \"Advanced\" section.": ["Select the method to use for form submissions. Additional options for the \"custom\" mode can be found in the \"Advanced\" section."], "- Custom -": ["- Custom -"], "Send email": ["Send email"], "Submissions method": ["Submissions method"], "There was an error submitting your form.": ["There was an error submitting your form."], "Your form has been submitted successfully": ["Your form has been submitted successfully"], "Focal point": ["Focal point"], "Caption text": ["Caption text"], "Currently, avoiding full page reloads is not possible when a Content block is present inside the Query block.": ["Currently, avoiding full page reloads is not possible when a Content block is present inside the Query block."], "Mark as nofollow": ["Mark as nofollow"], "Footnotes are not supported here. Add this block to post or page content.": ["Footnotes are not supported here. Add this block to post or page content."], "%s name": ["%s name"], "%s: Name": ["%s: Name"], "Specify how many links can appear before and after the current page number. Links to the first, current and last page are always visible.": ["Specify how many links can appear before and after the current page number. Links to the first, current and last page are always visible."], "Number of links": ["Number of links"], "Modified Date": ["Modified Date"], "Comments form disabled in editor.": ["Comments form disabled in editor."], "Block: Paragraph": ["Block: Paragraph"], "Edit Page List": ["Edit Page List"], "Unsaved Navigation Menu.": ["Unsaved Navigation Menu."], "Overlay menu controls": ["Overlay menu controls"], "The current menu options offer reduced accessibility for users and are not recommended. Enabling either \"Open on Click\" or \"Show arrow\" offers enhanced accessibility by allowing keyboard users to browse submenus selectively.": ["The current menu options offer reduced accessibility for users and are not recommended. Enabling either \"Open on Click\" or \"Show arrow\" offers enhanced accessibility by allowing keyboard users to browse submenus selectively."], "Navigation Menu: \"%s\"": ["Navigation Menu: \"%s\""], "Navigation Menu has been deleted or is unavailable. <button>Create a new Menu?</button>": ["Navigation Menu has been deleted or is unavailable. <button>Create a new Menu?</button>"], "It appears you are trying to use the deprecated Classic block. You can leave this block intact, or remove it entirely. Alternatively, you can refresh the page to use the Classic block.": ["It appears you are trying to use the deprecated Classic block. You can leave this block intact, or remove it entirely. Alternatively, you can refresh the page to use the Classic block."], "It appears you are trying to use the deprecated Classic block. You can leave this block intact, convert its content to a Custom HTML block, or remove it entirely. Alternatively, you can refresh the page to use the Classic block.": ["It appears you are trying to use the deprecated Classic block. You can leave this block intact, convert its content to a Custom HTML block, or remove it entirely. Alternatively, you can refresh the page to use the Classic block."], "… <a>Read more<span>: %1$s</span></a>": ["… <a>Read more<span>: %1$s</span></a>"], "The <main> element should be used for the primary content of your document only.": ["The <main> element should be used for the primary content of your document only."], "Enter fullscreen": ["Enter fullscreen"], "Parent": ["Parent"], "No title": ["No title"], "Are you sure you want to delete this Navigation Menu?": ["Are you sure you want to delete this Navigation Menu?"], "Footnote": ["Footnote"], "Footnotes found in blocks within this document will be displayed here.": ["Footnotes found in blocks within this document will be displayed here."], "Footnotes": ["Footnotes"], "Show label text": ["Show label text"], "No excerpt found": ["No excerpt found"], "Excerpt text": ["Excerpt text"], "The content is currently protected and does not have the available excerpt.": ["The content is currently protected and does not have the available excerpt."], "This block will display the excerpt.": ["This block will display the excerpt."], "Display a post's last updated date.": ["Display a post's last updated date."], "Only shows if the post has been modified": ["Only shows if the post has been modified"], "Post Modified Date": ["Post Modified Date"], "The relationship of the linked URL as space-separated link types.": ["The relationship of the linked URL as space-separated link types."], "Rel attribute": ["Rel attribute"], "Additional information to help clarify the purpose of the link.": ["Additional information to help clarify the purpose of the link."], "Choose or create a Navigation Menu": ["Choose or create a Navigation Menu"], "Lowercase Roman numerals": ["Lowercase Roman numerals"], "Uppercase Roman numerals": ["Uppercase Roman numerals"], "Lowercase letters": ["Lowercase letters"], "Uppercase letters": ["Uppercase letters"], "Numbers": ["Numbers"], "Post meta": ["Post meta"], "Max number of words": ["Max number of words"], "Image is contained without distortion.": ["Image is contained without distortion."], "Image covers the space evenly.": ["Image covers the space evenly."], "Arrange blocks in a grid.": ["Arrange blocks in a grid."], "Select the size of the source images.": ["Select the size of the source images."], "https://wordpress.org/documentation/article/embeds/": ["https://wordpress.org/documentation/article/embeds/"], "Write summary…": ["Write summary…"], "Open by default": ["Open by default"], "Type / to add a hidden block": ["Type / to add a hidden block"], "Add an image or video with a text overlay.": ["Add an image or video with a text overlay."], "Leave empty if decorative.": ["Leave empty if decorative."], "Reply to A WordPress Commenter": ["Reply to A WordPress Commenter"], "Commenter Avatar": ["Commenter Avatar"], "Scale option for dimensions control\u0004Cover": ["Cover"], "Scale option for dimensions control\u0004Contain": ["Contain"], "Aspect ratio": ["Aspect ratio"], "Aspect ratio option for dimensions control\u0004Original": ["Original"], "Resolution": ["Resolution"], "Grid": ["Grid"], "Attachment Pages": ["Attachment Pages"], "Transform paragraph to heading.": ["Transform paragraph to heading."], "Transform heading to paragraph.": ["Transform heading to paragraph."], "Import widget area": ["Import widget area"], "Unable to import the following widgets: %s.": ["Unable to import the following widgets: %s."], "Widget area: %s": ["Widget area: %s"], "Select widget area": ["Select widget area"], "Arrow option for Next/Previous link\u0004Chevron": ["Chevron"], "Arrow option for Next/Previous link\u0004Arrow": ["Arrow"], "Arrow option for Next/Previous link\u0004None": ["None"], "A decorative arrow for the next and previous link.": ["A decorative arrow for the next and previous link."], "Link author name to author page": ["Link author name to author page"], "Not available for aligned text.": ["Not available for aligned text."], "Choose a page to show only its subpages.": ["Choose a page to show only its subpages."], "Page List: \"%s\" page has no children.": ["Page List: \"%s\" page has no children."], "You have not yet created any menus. Displaying a list of your Pages": ["You have not yet created any menus. Displaying a list of your Pages"], "Untitled menu": ["Untitled menu"], "Structure for Navigation Menu: %s": ["Structure for Navigation Menu: %s"], "(no title %s)": ["(no title %s)"], "Group blocks together. Select a layout:": ["Group blocks together. Select a layout:"], "Classic Editor": ["Classic Editor"], "Remove caption": ["Remove caption"], "Add submenu link": ["Add submenu link"], "Start adding Heading blocks to create a table of contents. Headings with HTML anchors will be linked here.": ["Start adding Heading blocks to create a table of contents. Headings with HTML anchors will be linked here."], "Only including headings from the current page (if the post is paginated).": ["Only including headings from the current page (if the post is paginated)."], "Only include current page": ["Only include current page"], "Convert to static list": ["Convert to static list"], "Quote citation": ["Quote citation"], "Display the search results title based on the queried object.": ["Display the search results title based on the queried object."], "Search Results Title": ["Search Results Title"], "Search results for: “search term”": ["Search results for: \"search term\""], "Show search term in title": ["Show search term in title"], "Archive type: Name": ["Archive type: Name"], "Show archive type in title": ["Show archive type in title"], "Taxonomies": ["Taxonomies"], "Parents": ["Parents"], "Suffix": ["Suffix"], "Prefix": ["Prefix"], "Display last modified date": ["Display last modified date"], "Post Comments Link block: post not found.": ["Post Comments Link block: post not found."], "%s comment": ["%s comment", "%s comments"], "Post Comments Count block: post not found.": ["Post Comments Count block: post not found."], "To show a comment, input the comment ID.": ["To show a comment, input the comment ID."], "block title\u0004Post Comment": ["Post Comment"], "Link to author archive": ["Link to author archive"], "Author Name": ["Author Name"], "handle": ["handle"], "Import Classic Menus": ["Import Classic Menus"], "The Queen of Hearts.": ["The Queen of Hearts."], "The Mad Hatter.": ["The Mad Hatter."], "The Cheshire Cat.": ["The Cheshire Cat."], "The White Rabbit.": ["The White Rabbit."], "Alice.": ["<PERSON>."], "Gather blocks in a container.": ["Gather blocks in a container."], "Embed a podcast player from Pocket Casts.": ["oEmbed a podcast player from Pocket Casts."], "Overlay opacity": ["Overlay opacity"], "Switch to editable mode": ["Switch to editable mode"], "66 / 33": ["66 / 33"], "33 / 66": ["33 / 66"], "Show label": ["Show label"], "Post Comments Form block: Comments are not enabled for this item.": ["Post Comments Form block: Comments are not enabled for this item."], "Post Comments Form block: Comments are not enabled.": ["Post Comments Form block: Comments are not enabled."], "action that affects the current post\u0004Enable comments": ["Enable comments"], "Links are disabled in the editor.": ["Links are disabled in the editor."], "Commenter avatars come from <a>Gravatar</a>.": ["Commenter avatars come from <a>Gravatar</a>"], "This Navigation Menu is empty.": ["Navigation Menu is empty."], "Choose a %s": ["Choose a %s"], "Existing template parts": ["Existing template parts"], "Largest size": ["Largest size"], "Smallest size": ["Smallest size"], "Add text or blocks that will display when a query returns no results.": ["Add text or blocks that will display when the query returns no results."], "Choose a pattern": ["Choose a pattern"], "Choose a pattern for the query loop or start blank.": ["Choose a pattern for the query loop or start blank."], "Post type": ["Post type"], "Authors": ["Authors"], "Featured image: %s": ["Featured image: %s"], "Select the size of the source image.": ["Select the size of the source image."], "Link to post": ["Link to post"], "If there are any Custom Post Types registered at your site, the Content block can display the contents of those entries as well.": ["If there are any Custom Post Types registered at your site, the Content block can display the contents of those entries as well."], "That might be a simple arrangement like consecutive paragraphs in a blog post, or a more elaborate composition that includes image galleries, videos, tables, columns, and any other block types.": ["That might be a simple arrangement like consecutive paragraphs in a blog post, or a more elaborate composition that includes image galleries, videos, tables, columns, and any other block types."], "This is the Content block, it will display all the blocks in any single post or page.": ["This is the Content block, it will display all the blocks in any single post or page."], "Post Comments Form block: Comments are not enabled for this post type (%s).": ["Post Comments Form block: Comments are not enabled for this post type (%s)."], "To get started with moderating, editing, and deleting comments, please visit the Comments screen in the dashboard.": ["To get started with moderating, editing, and deleting comments, please visit the Comments screen in the dashboard."], "Hi, this is a comment.": ["<PERSON><PERSON><PERSON>, this is a comment."], "January 1, 2000 at 00:00 am": ["January 1, 2000 at 12:00 am"], "says": ["says"], "A WordPress Commenter": ["A WordPress Commenter"], "Author Biography": ["Author Biography"], "Convert to Link": ["Convert to <PERSON>"], "Navigation Menu successfully deleted.": ["Navigation Menu successfully deleted."], "Show arrow": ["Show arrow"], "Configure the visual appearance of the button that toggles the overlay menu.": ["Configure the visual appearance of the button that toggles the overlay menu."], "Show icon button": ["Show icon button"], "Classic menu import failed.": ["Classic menu import failed."], "Classic menu imported successfully.": ["Classic menu imported successfully."], "Classic menu importing.": ["Classic menu importing."], "Failed to create Navigation Menu.": ["Failed to create Navigation Menu."], "Navigation Menu successfully created.": ["Navigation Menu successfully created."], "Creating Navigation Menu.": ["Creating Navigation Menu."], "Unable to create Navigation Menu \"%s\".": ["Unable to create Navigation Menu \"%s\"."], "Unable to fetch classic menu \"%s\" from API.": ["Unable to fetch classic menu \"%s\" from API."], "Navigation block setup options ready.": ["Navigation block setup options ready."], "Loading navigation block setup options…": ["Loading navigation block setup options…"], "Create from '%s'": ["Create from '%s'"], "block example\u0004Home Link": ["Home Link"], "Add home link": ["Add home link"], "Home link text": ["Home link text"], "Arrange blocks vertically.": ["Arrange blocks vertically."], "Stack": ["<PERSON><PERSON>"], "Arrange blocks horizontally.": ["Arrange blocks horizontally."], "Responses to %s": ["Responses to %s"], "Response to %s": ["Response to %s"], "“Post Title”": ["\"Post Title\""], "Show comments count": ["Show comments count"], "Show post title": ["Show post title"], "Newer comments page link": ["Newer comments page link"], "Comments Pagination block: paging comments is disabled in the Discussion Settings": ["Comments Pagination block: paging comments is disabled in the Discussion Settings"], "Arrow option for Comments Pagination Next/Previous blocks\u0004Chevron": ["Chevron"], "Arrow option for Comments Pagination Next/Previous blocks\u0004Arrow": ["Arrow"], "Arrow option for Comments Pagination Next/Previous blocks\u0004None": ["None"], "A decorative arrow appended to the next and previous comments link.": ["A decorative arrow appended to the next and previous comments link."], "Older comments page link": ["Older comments page link"], "block title\u0004Comment Date": ["Comment Date"], "Link to comment": ["Link to comment"], "block title\u0004Comment Content": ["Comment Content"], "block title\u0004Comment Author": ["Comment Author"], "Link to authors URL": ["Link to authors URL"], "Link to user profile": ["Link to user profile"], "Select the avatar user to display, if it is blank it will use the post/page author.": ["Select the avatar user to display, if it is blank it will use the post/page author."], "Default Avatar": ["De<PERSON><PERSON>"], "Week": ["Week"], "single horizontal line\u0004Row": ["Row"], "Responses": ["Responses"], "Response": ["Response"], "Older Comments": ["Older Comments"], "Newer Comments": ["Newer Comments"], "%s Avatar": ["%s Avatar"], "Icon background": ["Icon background"], "Page List: Cannot retrieve Pages.": ["Page List: Cannot retrieve Pages."], "Icon": ["Icon"], "Use as Site Icon": ["Use as Site Icon"], "Site Icons are what you see in browser tabs, bookmark bars, and within the WordPress mobile apps. To use a custom icon that is different from your site logo, use the <a>Site Icon settings</a>.": ["Site Icons are what you see in browser tabs, bookmark bars, and within the WordPress mobile apps. To use a custom icon that is different from your site logo, use the <a>Site Icon settings</a>."], "You do not have permission to create Navigation Menus.": ["You do not have permission to create Navigation Menus."], "You do not have permission to edit this Menu. Any changes made will not be saved.": ["You do not have permission to edit this Menu. Any changes made will not be saved."], "Loading…": ["Loading…"], "Preload value\u0004None": ["None"], "Term Description": ["Term Description"], "Default based on area (%s)": ["Default based on area (%s)"], "Area": ["Area"], "Choose an existing %s or create a new one.": ["Choose an existing %s or create a new one."], "Untitled Template Part": ["Untitled Template Part"], "Template Part \"%s\" inserted.": ["Template Part \"%s\" inserted."], "Number of tags": ["Number of tags"], "Make title link to home": ["Make title link to home"], "Arrow option for Query Pagination Next/Previous blocks\u0004Chevron": ["Chevron"], "Arrow option for Query Pagination Next/Previous blocks\u0004Arrow": ["Arrow"], "Arrow option for Query Pagination Next/Previous blocks\u0004None": ["None"], "A decorative arrow appended to the next and previous page link.": ["A decorative arrow is appended to the next and previous page link."], "Arrow": ["Arrow"], "Post Title": ["Post Title"], "Enter character(s) used to separate terms.": ["Enter character(s) used to separate terms."], "Displays the post link that precedes the current post.": ["Displays the post link that precedes the current post."], "Displays the post link that follows the current post.": ["Displays the post link that follows the current post."], "Include the label as part of the link": ["Include the label as part of the link"], "If you have entered a custom label, it will be prepended before the title.": ["If you have entered a custom label, it will be prepended before the title."], "Display the title as a link": ["Display the title as a link"], "Previous post": ["Previous post"], "Next post": ["Next post"], "Previous: ": ["Previous: "], "Next: ": ["Next: "], "Image scaling options\u0004Scale": ["Scale"], "Image will be stretched and distorted to completely fill the space.": ["Image will be stretched and distorted to completely fill the space."], "Image is scaled to fill the space without clipping nor distorting.": ["Image is scaled to fill the space without clipping nor distorting."], "Image is scaled and cropped to fill the entire space without being distorted.": ["Image is scaled and cropped to fill the entire space without being distorted."], "Scale option for Image dimension control\u0004Fill": ["Fill"], "Scale option for Image dimension control\u0004Contain": ["Contain"], "Scale option for Image dimension control\u0004Cover": ["Cover"], "Post Date": ["Post Date"], "No comments": ["No comments"], "Post Author": ["Post Author"], "Write byline…": ["Write byline…"], "Post author byline text": ["Post author byline text"], "Show bio": ["Show bio"], "Avatar size": ["Avatar size"], "Show avatar": ["Show avatar"], "navigation link preview example\u0004Example Link": ["Example Link"], "Create draft page: <mark>%s</mark>": ["Create draft page: <mark>%s</mark>"], "Create draft post: <mark>%s</mark>": ["Create draft post: <mark>%s</mark>"], "Navigation link text": ["Navigation link text"], "Add submenu": ["Add submenu"], "Add link": ["Add link"], "Select tag": ["Select tag"], "Select category": ["Select category"], "Select page": ["Select page"], "Select post": ["Select post"], "Contact": ["Contact Us"], "Submenu & overlay background": ["Submenu & overlay background"], "Submenu & overlay text": ["Submenu & overlay text"], "Open on click": ["Open on click"], "Submenus": ["Submenus"], "Always": ["Always"], "Off": ["Off"], "Collapses the navigation options in a menu icon opening an overlay.": ["Collapses the navigation options in a menu icon opening an overlay."], "Configure overlay menu": ["Configure overlay menu"], "Overlay Menu": ["Overlay Menu"], "Display": ["Display"], "Delete menu": ["Delete menu"], "Menu name": ["Menu name"], "Manage menus": ["Manage menus"], "Create new Menu": ["Create new <PERSON>u"], "Switch to '%s'": ["Switch to '%s'"], "Start empty": ["Start empty"], "The <footer> element should represent a footer for its nearest sectioning element (e.g.: <section>, <article>, <main> etc.).": ["The <footer> element should represent a footer for its nearest sectioning element (e.g.: <section>, <article>, <main> etc.)."], "The <aside> element should represent a portion of a document whose content is only indirectly related to the document's main content.": ["The <aside> element should represent a portion of a document whose content is only indirectly related to the document's main content."], "The <article> element should represent a self-contained, syndicatable portion of the document.": ["The <article> element should represent a self-contained, syndicatable portion of the document."], "The <section> element should represent a standalone portion of the document that can't be better represented by another element.": ["The <section> element should represent a standalone portion of the document that can't be better represented by another element."], "The <header> element should represent introductory content, typically a group of introductory or navigational aids.": ["The <header> element should represent introductory content, typically a group of introductory or navigational aids."], "Loading options…": ["Loading options…"], "All gallery image sizes updated to: %s": ["All gallery image sizes updated to: %s"], "All gallery images updated to not open in new tab": ["All gallery images updated to not open in new tab"], "All gallery images updated to open in new tab": ["All gallery images updated to open in new tab"], "All gallery image links updated to: %s": ["All gallery image links updated to: %s"], "If uploading to a gallery all files need to be image formats": ["If uploading to a gallery all files need to be image formats"], "Media item link option\u0004None": ["None"], "Name of the file\u0004Armstrong_Small_Step": ["<PERSON>_Small_Step"], "Embed Wolfram notebook content.": ["Embed Wolfram notebook content."], "Embed Pinterest pins, boards, and profiles.": ["Embed Pinterest pins, boards, and profiles."], "bookmark": ["bookmark"], "No published posts found.": ["No published posts found."], "noun; Audio block parameter\u0004Preload": ["Preload"], "Remove %s": ["Remove %s"], "Template part has been deleted or is unavailable: %s": ["Template part has been deleted or is unavailable: %s"], "Close menu": ["Close menu"], "Open menu": ["Open menu"], "Term items not found.": ["Term items not found."], "Show link on new line": ["Show link on new line"], "Add \"read more\" link text": ["Add \"read more\" link text"], "Change Date": ["Change Date"], "Make title a link": ["Make title a link"], "An example title": ["An example title"], "Previous Page": ["Previous Page"], "Previous page link": ["Previous page link"], "Next Page": ["Next Page"], "Next page link": ["Next page link"], "Display the archive title based on the queried object.": ["Display the archive title based on the queried object."], "Archive Title": ["Archive Title"], "Archive title": ["Archive title"], "Provided type is not supported.": ["Provided type is not supported."], "Image, Date, & Title": ["Image, Date, & Title"], "Title, Date, & Excerpt": ["Title, Date, & Excerpt"], "Title & Excerpt": ["Title & Excerpt"], "Title & Date": ["Title & Date"], "Keyword": ["Keyword"], "Filters": ["Filters"], "Sticky posts": ["Sticky posts"], "Only": ["Only"], "Exclude": ["Exclude"], "Limit the pages you want to show, even if the query has more results. To show all pages use 0 (zero).": ["Limit the pages you want to show, even if the query has more results. To show all pages use 0 (zero)."], "Offset": ["Offset"], "Site Title placeholder": ["Site Title placeholder"], "Write site title…": ["Write site title…"], "Site title text": ["Site title text"], "Site Tagline placeholder": ["Site Tagline placeholder"], "Write site tagline…": ["Write site tagline…"], "Site tagline text": ["Site tagline text"], "Link image to home": ["Link image to home"], "Image width": ["Image width"], "Default (<div>)": ["Default (<div>)"], "HTML element": ["HTML element"], "Redirect to current URL": ["Redirect to current URL"], "Display login as form": ["Display log in as form"], "Media width": ["Media width"], "Embed of %s.": ["Embed of %s."], "PDF embed": ["PDF embed"], "Embed of the selected PDF file.": ["Embed of the selected PDF file."], "Copied URL to clipboard.": ["Copied URL to clipboard."], "Note: Most phone and tablet browsers won't display embedded PDFs.": ["Note: Most phone and tablet browsers won't display embedded PDFs."], "Show inline embed": ["Show inline embed"], "PDF settings": ["PDF settings"], "%1$s (%2$d of %3$d)": ["%1$s (%2$d of %3$d)"], "Autoplay may cause usability issues for some users.": ["Autoplay may cause usability issues for some users."], "Add citation": ["Add citation"], "Add quote": ["Add quote"], "Add caption": ["Add caption"], "Choose": ["<PERSON><PERSON>"], "Start blank": ["Start blank"], "Select %s": ["Select %s"], "Type / to choose a block": ["Type / to choose a block"], "Templates": ["Templates"], "This content is password protected.": ["This content is password protected."], "Icon color": ["Icon Colour"], "Huge": ["<PERSON>ge"], "Normal": ["Normal"], "Wood thrush singing in Central Park, NYC.": ["Wood thrush singing in Central Park, NYC."], "Video caption text": ["Video caption text"], "Write verse…": ["Write verse…"], "Verse text": ["Verse text"], "Column %d text": ["Column %d text"], "Table caption text": ["Table caption text"], "Footer cell text": ["Footer cell text"], "Body cell text": ["Body cell text"], "Header cell text": ["Header cell text"], "Shortcode text": ["Shortcode text"], "Block cannot be rendered inside itself.": ["Block cannot be rendered inside itself."], "Pullquote citation text": ["Pullquote citation text"], "Pullquote text": ["Pullquote text"], "Preformatted text": ["Preformatted text"], "List text": ["List text"], "Ordered": ["Ordered"], "Unordered": ["Unordered"], "Download button text": ["Download button text"], "Audio caption text": ["Audio caption text"], "Gallery caption text": ["Gallery caption text"], "Image caption text": ["Image caption text"], "Add text over image": ["Add text over image"], "More": ["More"], "Open links in new tab": ["Open links in new tab"], "Convert to blocks": ["Convert to blocks"], "Add tracks": ["Add tracks"], "Remove track": ["Remove track"], "Kind": ["Kind"], "Language tag (en, fr, etc.)": ["Language tag (en, fr, etc)"], "Source language": ["Source language"], "Title of track": ["Title of track"], "Label": ["Label"], "Edit track": ["Edit track"], "Text tracks": ["Text tracks"], "Tracks can be subtitles, captions, chapters, or descriptions. They help make your content more accessible to a wider range of users.": ["Tracks can be subtitles, captions, chapters, or descriptions. They help make your content more accessible to a wider range of users."], "Captions": ["Captions"], "Subtitles": ["Subtitles"], "Use button with icon": ["Use button with icon"], "Button inside": ["<PERSON><PERSON> inside"], "Button outside": ["Button outside"], "No button": ["No button"], "Change button position": ["Change button position"], "“Read more” link text": ["“Read more” link text"], "Add link to featured image": ["Add link to featured image"], "Repeated background": ["Repeated background"], "One column": ["One column"], "100": ["100"], "Find out more": ["Find out more"], "by %s": ["by %s"], "Comment": ["Comments"], "Display author name": ["Display author name"], "Change content position": ["Change content position"], "25 / 50 / 25": ["25 / 50 / 25"], "33 / 33 / 33": ["33 / 33 / 33"], "50 / 50": ["50 / 50"], "This column count exceeds the recommended amount and may cause visual breakage.": ["This column count exceeds the recommended amount and may cause visual breakage."], "Browser default": ["Browser default"], "Crop": ["Crop"], "Image uploaded.": ["Image uploaded."], "survey": ["survey"], "social": ["social"], "Design": ["Design"], "Mobile": ["Mobile"], "Block variations": ["Block variations"], "Template Part": ["Template Part"], "Add media": ["Add media"], "%1$s (%2$s)": ["%1$s (%2$s)"], "Button only": ["Button Only"], "Social Icon": ["Social Icon"], "Select poster image": ["Select poster image"], "Poster image": ["Poster image"], "WHAT was he doing, the great god Pan,\n\tDown in the reeds by the river?\nSpreading ruin and scattering ban,\nSplashing and paddling with hoofs of a goat,\nAnd breaking the golden lilies afloat\n    With the dragon-fly on the river.": ["I am an African\nFor her blue skies take my breath away\nAnd my hope for the future is bright\nI am an African\nFor her people greet me as family\nAnd teach me the meaning of community\nI am an African\nFor her wildness quenches my spirit\nAnd brings me closer to the source of life"], "Footer label": ["Footer label"], "Header label": ["Header label"], "Matt Mullenweg": ["<PERSON>"], "EXT. XANADU - FAINT DAWN - 1940 (MINIATURE)\nWindow, very small in the distance, illuminated.\nAll around this is an almost totally black screen. Now, as the camera moves slowly towards the window which is almost a postage stamp in the frame, other forms appear;": ["EXT. XANADU - FAINT DAWN - 1940 (MINIATURE)\nWindow, very small in the distance, illuminated.\nAll around this is an almost totally black screen. Now, as the camera moves slowly towards the window which is almost a postage stamp in the frame, other forms appear;"], "Image alignment": ["Image alignment"], "Display featured image": ["Display featured image"], "Full post": ["Full post"], "Media file": ["Media file"], "Suspendisse commodo neque lacus, a dictum orci interdum et.": ["Suspendisse commodo neque lacus, a dictum orci interdum et."], "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent et eros eu felis.": ["Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent et eros eu felis."], "// A “block” is the abstract term used\n// to describe units of markup that\n// when composed together, form the\n// content or layout of a page.\nregisterBlockType( name, settings );": ["// A “block” is the abstract term used\n// to describe units of markup that\n// when composed together, form the\n// content or layout of a page.\nregisterBlockType( name, settings );"], "podcast": ["podcast"], "Link to": ["Link to"], "Level %1$s. %2$s": ["Level %1$s. %2$s"], "Level %s. Empty.": ["Level %s. Empty."], "(Note: many devices and browsers do not display this text.)": ["(Note: many devices and browsers do not display this text.)"], "Describe the role of this image on the page.": ["Describe the role of this image on the page."], "Title attribute": ["Title attribute"], "Embed a TikTok video.": ["Embed a TikTok video."], "Open Media Library": ["Open Media Library"], "Image size": ["Image size"], "Small": ["Small"], "Sorting and filtering": ["Sorting and Filtering"], "Post content": ["Post Content"], "menu": ["menu"], "Percentage Width": ["Percentage Width"], "There is no poster image currently selected": ["There is no poster image currently selected"], "The current poster image url is %s": ["The current poster image url is %s"], "Play inline": ["Play inline"], "Nam risus massa, ullamcorper consectetur eros fermentum, porta aliquet ligula. Sed vel mauris nec enim.": ["Nam risus massa, ullamcorper consectetur eros fermentum, porta aliquet ligula. Sed vel mauris nec enim ultricies commodo."], "Etiam et egestas lorem. Vivamus sagittis sit amet dolor quis lobortis. Integer sed fermentum arcu, id vulputate lacus. Etiam fermentum sem eu quam hendrerit.": ["Etiam et egestas lorem. Vivamus sagittis sit amet dolor quis lobortis. Integer sed fermentum arcu, id vulputate lacus. Etiam fermentum sem eu quam hendrerit."], "Three columns; wide center column": ["Three columns; wide center column"], "Three columns; equal split": ["Three columns; equal split"], "Two columns; two-thirds, one-third split": ["Two columns; two-thirds, one-third split"], "Two columns; one-third, two-thirds split": ["Two columns; one-third, two-thirds split"], "Two columns; equal split": ["Two columns; equal split"], "Link rel": ["<PERSON> rel"], "Welcome to the wonderful world of blocks…": ["Welcome to the wonderful world of blocks…"], "In quoting others, we cite ourselves.": ["In quoting others, we cite ourselves."], "Start value": ["Start Value"], "One of the hardest things to do in technology is disrupt yourself.": ["One of the hardest things to do in technology is disrupt yourself."], "Open in new tab": ["Open in new tab"], "Attachment page": ["Attachment page"], "December 6, 2018": ["December 6, 2018"], "February 21, 2019": ["February 21, 2019"], "May 7, 2019": ["May 7, 2019"], "Release Date": ["Release Date"], "Jazz Musician": ["Jazz Musician"], "Footer section": ["Footer section"], "Header section": ["Header section"], "Change column alignment": ["Change column alignment"], "Create Table": ["Create Table"], "Insert a table for sharing data.": ["Insert a table for sharing data."], "Align column right": ["Align column right"], "Align column center": ["Align column centre"], "Align column left": ["Align column left"], "<strong>Snow Patrol</strong>": ["<strong>Snow Patrol</strong>"], "Mont Blanc appears—still, snowy, and serene.": ["Mont Blanc appears—still, snowy, and serene."], "— Kobayashi Issa (一茶)": ["<PERSON> <PERSON><PERSON> (一茶)"], "The wren<br>Earns his living<br>Noiselessly.": ["The wren<br>Earns his living<br>Noiselessly."], "Describe the purpose of the image.": ["Describe the purpose of the image."], "Code is Poetry": ["Code is Poetry"], "In a village of La Mancha, the name of which I have no desire to call to mind, there lived not long since one of those gentlemen that keep a lance in the lance-rack, an old buckler, a lean hack, and a greyhound for coursing.": ["In a village of La Mancha, the name of which I have no desire to call to mind, there lived not long since one of those gentlemen that keep a lance in the lance-rack, an old buckler, a lean hack, and a greyhound for coursing."], "Six.": ["Six."], "Five.": ["Five."], "Four.": ["Four."], "Three.": ["Three."], "Two.": ["Two."], "One.": ["One."], "Group": ["Group"], "Learn more about embeds": ["Learn more about embeds"], "Paste a link to the content you want to display on your site.": ["Paste a link to the content you want to display on your site."], "- Select -": ["- Select -"], "Display entries from any RSS or Atom feed.": ["Display entries from any RSS or Atom feed."], "Max number of words in excerpt": ["Max number of words in excerpt"], "Display excerpt": ["Display excerpt"], "Display date": ["Display date"], "Display author": ["Display author"], "Edit RSS URL": ["Edit RSS URL"], "Enter URL here…": ["Enter URL here…"], "Add button text…": ["Add button text…"], "Button text": ["Button text"], "Optional placeholder…": ["Optional placeholder…"], "Optional placeholder text": ["Optional placeholder text"], "Add label…": ["Add label…"], "Label text": ["Label text"], "Hide the excerpt on the full content page": ["Hide the excerpt on the full content page"], "The excerpt is visible.": ["The excerpt is visible."], "The excerpt is hidden.": ["The excerpt is hidden."], "Embedded content from %s can't be previewed in the editor.": ["Embedded content from %s can't be previewed in the editor."], "Sorry, this content could not be embedded.": ["Sorry, this content could not be embedded."], "Embed Amazon Kindle content.": ["Embed Amazon Kindle content."], "ebook": ["ebook"], "Embed Crowdsignal (formerly Polldaddy) content.": ["Embed Crowdsignal (formerly Polldaddy) content."], "%s minute": ["%s minute", "%s minutes"], "content placeholder\u0004Content…": ["Content…"], "button label\u0004Convert to link": ["Convert to link"], "button label\u0004Try again": ["Try again"], "This image has an empty alt attribute": ["This image has an empty alt attribute"], "This image has an empty alt attribute; its file name is %s": ["This image has an empty alt attribute; its file name is %s"], "Empty block; start writing or type forward slash to choose a block": ["Empty block; start writing or type forward slash to choose a block"], "Stack on mobile": ["Stack on mobile"], "Link removed.": ["<PERSON> removed."], "Playback controls": ["Playback Controls"], "Muted": ["Muted"], "New Column": ["New Column"], "Fixed width table cells": ["Fixed width table cells"], "Edit table": ["Edit table"], "Create": ["Create"], "Row count": ["Row count"], "Column count": ["Column count"], "Height in pixels": ["Height in pixels"], "Write shortcode here…": ["Write shortcode here…"], "Shortcode": ["Shortcode"], "Separator": ["Separator"], "Write preformatted text…": ["Write preformatted text…"], "Drop cap": ["Drop Cap"], "Showing large initial letter.": ["Showing large initial letter."], "Keep as HTML": ["Keep as HTML"], "Media area": ["Media area"], "Show media on right": ["Show media on right"], "Show media on left": ["Show media on left"], "Indent list item": ["Indent list item"], "Outdent list item": ["Outdent list item"], "Convert to ordered list": ["Convert to ordered list"], "Convert to unordered list": ["Convert to unordered list"], "Latest Posts": ["Latest Posts"], "Display post date": ["Display post date"], "Number of comments": ["Number of Comments"], "Display avatar": ["Display Avatar"], "Edit image": ["Edit image"], "Write HTML…": ["Write HTML…"], "Heading": ["Heading"], "Show download button": ["Show download button"], "button label\u0004Download": ["Download"], "Copy URL": ["Copy URL"], "Write file name…": ["Write file name…"], "block title\u0004Embed": ["Embed"], "Embedded content from %s": ["Embedded content from %s"], "button label\u0004Embed": ["Embed"], "Enter URL to embed here…": ["Enter URL to embed here…"], "%s URL": ["%s URL"], "Resize for smaller devices": ["Resize for smaller devices"], "Media settings": ["Media Settings"], "Edit URL": ["Edit URL"], "This embed may not preserve its aspect ratio when the browser is resized.": ["This embed may not preserve its aspect ratio when the browser is resized."], "This embed will preserve its aspect ratio when the browser is resized.": ["This embed will preserve its aspect ratio when the browser is resized."], "Embed a WordPress.tv video.": ["Embed a WordPress.tv video."], "Embed a VideoPress video.": ["Embed a VideoPress video."], "Embed a Tumblr post.": ["Embed a Tumblr post."], "Embed a TED video.": ["Embed a TED video."], "Embed Speaker Deck content.": ["Embed Speaker Deck content."], "Embed SmugMug content.": ["Embed SmugMug content."], "Embed Scribd content.": ["Embed Scribd content."], "Embed ReverbNation content.": ["Embed ReverbNation content."], "Embed a Reddit thread.": ["Embed a Reddit thread."], "Embed Mixcloud content.": ["Embed Mixcloud content."], "Embed Kickstarter content.": ["Embed Kickstarter content."], "Embed Issuu content.": ["Embed Issuu content."], "Embed Imgur content.": ["Embed Imgur content."], "Embed a Dailymotion video.": ["Embed a Dailymotion video."], "Embed CollegeHumor content.": ["Embed CollegeHumor content."], "Embed Cloudup content.": ["Embed Cloudup content."], "Embed an Animoto video.": ["Embed an Animoto video."], "Embed a Vimeo video.": ["Embed a Vimeo video."], "Embed Flickr content.": ["Embed Flickr content."], "Embed Spotify content.": ["Embed Spotify content."], "Embed SoundCloud content.": ["Embed SoundCloud content."], "audio": ["audio"], "blog": ["blog"], "post": ["post"], "Embed an Instagram post.": ["Embed an Instagram post."], "image": ["image"], "Embed a Facebook post.": ["Embed a Facebook post."], "Embed a YouTube video.": ["Embed a YouTube video."], "video": ["video"], "music": ["music"], "Embed a tweet.": ["Embed a tweet."], "Write title…": ["Write title…"], "Overlay": ["Overlay"], "Fixed background": ["Fixed Background"], "Cover": ["Cover"], "Write code…": ["Write code…"], "Classic": ["Classic Edit"], "Display a list of all categories.": ["Display a list of all categories."], "Add text…": ["Add text…"], "Block has been deleted or is unavailable.": ["Block has been deleted or is unavailable."], "button label\u0004Import": ["Import"], "Options": ["Options"], "Terms": ["Terms"], "Avatar": ["Avatar"], "Chat": ["Cha<PERSON>"], "Standard": ["Standard"], "Aside": ["Aside"], "Add block": ["Add block"], "Number of items": ["Number of items"], "Z → A": ["Z \t A"], "A → Z": ["A \t Z"], "Oldest to newest": ["Oldest to Newest"], "Newest to oldest": ["Newest to Oldest"], "Order by": ["Order by"], "Reset": ["Reset"], "Enter your email address.": ["Enter your email address."], "Gallery": ["Gallery"], "English": ["English"], "Chapters": ["Chapters"], "Day": ["Day"], "Month": ["Month"], "Show tag counts": ["Show tag counts"], "Table of Contents": ["Table of Contents"], "Contact us": ["Contact us"], "Search results": ["Search results"], "(Untitled)": ["(Untitled)"], "Choose logo": ["Choose logo"], "Read more": ["Read more"], "Embed a WordPress post.": ["Embed a WordPress post."], "Show": ["Show"], "Taxonomy": ["Taxonomy"], "The description will be displayed in the menu if the current theme supports it.": ["The description will be displayed in the menu if the current theme supports it."], "%1$s response to %2$s": ["%1$s response to %2$s", "%1$s responses to %2$s"], "Minimum height": ["Minimum height"], "Previous": ["Previous"], "Year": ["Year"], "editor button\u0004Left to right": ["Left to right"], "Back": ["Back"], "Autoplay": ["Autoplay"], "Metadata": ["<PERSON><PERSON><PERSON>"], "Auto": ["Auto"], "Table": ["Table"], "Tools": ["Tools"], "File": ["File"], "Delete column": ["Delete column"], "Replace": ["Replace"], "Page break": ["Page break"], "Formats": ["Formats"], "Move up": ["Move up"], "Move down": ["Move down"], "Status": ["Status"], "Menu": ["<PERSON><PERSON>"], "Invalid": ["Invalid"], "Empty": ["Empty"], "Reverse order": ["Reverse order"], "Video": ["Video"], "Audio": ["Audio"], "Columns": ["Columns"], "Large": ["Large"], "Add image": ["Add image"], "Include": ["Include"], "Remove": ["Remove"], "Featured image": ["Featured Image"], "Navigation": ["Navigation"], "Exit fullscreen": ["Exit fullscreen"], "Link": ["Link"], "Link to %s": ["Link to %s"], "Preload": ["Preload"], "Display as dropdown": ["Display as dropdown"], "Descriptions": ["Descriptions"], "User": ["User"], "%s response": ["%s response", "%s responses"], "One response": ["One response"], "Add a featured image": ["Add a featured image"], "Custom": ["Custom"], "One response to %s": ["One response to %s"], "Menus": ["Menus"], "Next": ["Next"], "Image": ["Image"], "Home": ["Home"], "Next Post": ["Next Post"], "Previous Post": ["Previous Post"], "Quote": ["Quote"], "Background": ["Background"], "Loop": ["Loop"], "List": ["List"], "Unlink": ["Unlink"], "Indent": ["Indent"], "Outdent": ["Outdent"], "Code": ["Code"], "Font size": ["Font Size"], "Insert column after": ["Insert column after"], "Insert column before": ["Insert column before"], "Delete row": ["Delete row"], "Insert row after": ["Insert row after"], "Insert row before": ["Insert row before"], "Log out": ["Log out"], "Show hierarchy": ["Show hierarchy"], "Text": ["Text"], "Calendar": ["Calendar"], "Show post counts": ["Show post counts"], "Post Comment": ["Post Comment"], "Leave a Reply": ["Leave a Reply"], "Posted by": ["Posted by"], "No results found.": ["No results found."], "HTML": ["HTML"], "Default": ["<PERSON><PERSON><PERSON>"], "Height": ["Height"], "Date Format": ["Date Format"], "Email": ["Email"], "Settings": ["Settings"], "About": ["About"], "Width": ["<PERSON><PERSON><PERSON>"], "(no title)": ["(no title)"], "Select": ["Select"], "Close": ["Close"], "Version": ["Version"], "Search": ["Search"], "Add": ["Add"], "OK": ["OK"], "Media": ["Media"], "Alternative text": ["Alternative text"], "Size": ["Size"], "Right": ["Right"], "Center": ["Centre"], "Left": ["Left"], "Reply": ["Reply"], "Cancel": ["Cancel"], "Preview": ["Preview"], "Submit": ["Submit"], "Anonymous": ["Anonymous"], "None": ["None"], "Description": ["Description"], "Name": ["Name"], "Save": ["Save"], "Delete": ["Delete"], "Excerpt": ["Excerpt"], "Apply": ["Apply"], "Edit": ["Edit"], "URL": ["URL"], "Author": ["Author"], "Draft": ["Draft"], "Title": ["Title"], "No posts found.": ["No posts found."]}}, "comment": {"reference": "wp-includes/js/dist/block-library.js"}}